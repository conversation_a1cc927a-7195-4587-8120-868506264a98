// Integration Guide: Complete Agent System Setup
// File: components/agents/complete-integration.tsx

import React, { useState, useEffect } from 'react';
import { CompleteAgentManager } from './agent-manager-complete';
import { AgentIntegration } from './agent-integration';
import { SettingsManager } from '../settings/settings-manager';
import { SettingsUI } from '../settings/settings-ui';
import { getGlobalSettingsManager } from '../settings/global-settings';
import { SharedAgentStateProvider, useSharedAgentState } from './shared-agent-state';
import { useSystemSettings } from '../settings/settings-context';
import { useTimeout } from '../../lib/utils/use-timeout';
import { useDebug } from '../../lib/utils/use-debug';
import { useTelemetry } from '../../lib/utils/use-telemetry';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { AlertTriangle, CheckCircle, Settings, Activity, BarChart, Brain, Play, RefreshCw } from 'lucide-react';
import AgentHistoryTab from '../history/AgentHistoryTab';
import AgentAnalyticsTab from '../analytics/AgentAnalyticsTab';

interface CompleteSystemProps {
  className?: string;
}

// Wrapper component that provides shared state
export const CompleteAgentSystem: React.FC<CompleteSystemProps> = ({ className }) => {
  return (
    <SharedAgentStateProvider>
      <CompleteAgentSystemInner className={className} />
    </SharedAgentStateProvider>
  );
};

// Inner component that uses shared state
const CompleteAgentSystemInner: React.FC<CompleteSystemProps> = ({ className }) => {
  const sharedState = useSharedAgentState();
  const [agentManager] = useState(() => new CompleteAgentManager()); // Now uses IPC bridge internally
  const [settingsManager] = useState(() => getGlobalSettingsManager());
  const [showSettings, setShowSettings] = useState(false);

  // ✅ Safe access to settings with fallback
  let systemSettings: any;
  let defaultTimeout: number;
  let maxConcurrentTasks: number;
  let debugMode: boolean;
  let enableTelemetry: boolean;
  let withDefaultTimeout: any;

  try {
    const settingsContext = useSystemSettings();
    systemSettings = settingsContext.systemSettings;
    const timeoutUtils = useTimeout();
    defaultTimeout = timeoutUtils.defaultTimeout;
    maxConcurrentTasks = systemSettings.maxConcurrentTasks || 3;
    debugMode = systemSettings.debugMode || false;
    enableTelemetry = systemSettings.enableTelemetry || false;
    withDefaultTimeout = timeoutUtils.withDefaultTimeout;
  } catch (error) {
    console.warn('Settings context not available, using fallbacks:', error);
    // ✅ Fallback values when settings context is not available
    systemSettings = {
      defaultTimeout: 30000,
      maxConcurrentTasks: 3,
      debugMode: false,
      enableTelemetry: false
    };
    defaultTimeout = 30000;
    maxConcurrentTasks = 3;
    debugMode = false;
    enableTelemetry = false;
    withDefaultTimeout = async (promise: Promise<any>, label: string) => {
      console.log(`⚠️ Using fallback timeout for: ${label}`);
      return promise; // No timeout when settings unavailable
    };
  }

  // ✅ Update agent manager concurrency limit when settings change
  useEffect(() => {
    if (agentManager && maxConcurrentTasks) {
      agentManager.updateConcurrencyLimit(maxConcurrentTasks);
      console.log(`🔄 Updated agent manager concurrency limit to ${maxConcurrentTasks}`);
    }
  }, [agentManager, maxConcurrentTasks]);

  // ✅ Initialize debug and telemetry hooks (they automatically sync with Electron when available)
  const debug = useDebug();
  const telemetry = useTelemetry();

  // Derive system metrics from shared state with null safety
  const systemMetrics = {
    systemHealthScore: sharedState.agents.length > 0
      ? sharedState.agents.reduce((acc, agent) => {
          const healthScore = isNaN(agent.healthScore) ? 0 : (agent.healthScore || 0);
          return acc + healthScore;
        }, 0) / sharedState.agents.length
      : 0,
    activeAgents: sharedState.agents.filter(agent => agent.status === 'busy').length,
    queueLength: sharedState.tasks.filter(task => task.status === 'pending').length,
    totalTasks: sharedState.tasks.length,
    successfulTasks: sharedState.tasks.filter(task => task.status === 'completed').length,
    averageResponseTime: 2000, // Mock value
    totalTokensUsed: sharedState.agents.reduce((acc, agent) => {
      const tokensUsed = isNaN(agent.tokensUsed) ? 0 : (agent.tokensUsed || 0);
      return acc + tokensUsed;
    }, 0)
  };

  // Mock optimizations for now
  const optimizations = agentManager.getOptimizationSuggestions ? agentManager.getOptimizationSuggestions() : [];

  useEffect(() => {
    // Listen for system messages
    const handleMessage = (message: any) => {
      console.log('System message:', message);
      // Add message to shared state
      sharedState.addMessage({
        agentId: message.agentId || 'system',
        message: message.message || message.toString(),
        timestamp: Date.now(),
        type: message.type || 'info'
      });
    };

    agentManager.onMessage(handleMessage);

    return () => {
      agentManager.offMessage(handleMessage);
    };
  }, [agentManager, sharedState]);

  const handleTaskSubmission = async (task: string) => {
    try {
      console.log(`🕐 Starting task submission with timeout: ${defaultTimeout}ms`);

      // ✅ Wrap task submission with timeout
      return await withDefaultTimeout(
        (async () => {
          // Check if this is a Micromanager task that needs decomposition
          if (sharedState.selectedAgent === 'micromanager') {
            return await handleMicromanagerTask(task);
          } else {
            // Direct agent assignment for non-Micromanager tasks
            await sharedState.assignTask({
              agentId: sharedState.selectedAgent,
              description: task,
              status: 'pending',
              priority: 'medium'
            });

            const taskId = await agentManager.submitTask(task);
            console.log(`Task submitted with ID: ${taskId}`);
            return taskId;
          }
        })(),
        `Task submission for ${sharedState.selectedAgent}`
      );
    } catch (error) {
      console.error('Task submission failed:', error);
      throw error;
    }
  };

  const handleMicromanagerTask = async (task: string) => {
    try {
      // Import TaskOrchestrator dynamically to avoid circular dependencies
      const { TaskOrchestrator } = await import('./task-orchestrator');

      // Decompose the task into subtasks
      const decomposition = TaskOrchestrator.decompose(task);

      console.log(`Micromanager decomposed task into ${decomposition.subtasks.length} subtasks:`, decomposition);

      // Create parent task in shared state
      await sharedState.assignTask({
        agentId: 'micromanager',
        description: `[ORCHESTRATOR] ${task}`,
        status: 'pending',
        priority: 'high'
      });

      // Submit parent task to Micromanager for orchestration
      const parentTaskId = await agentManager.submitTask(
        task,
        undefined,
        'high',
        {
          decomposition,
          isOrchestrationTask: true,
          originalTaskId: decomposition.parentTaskId
        }
      );

      // Create Kanban cards for all subtasks
      const { KanbanTaskBridge } = await import('./kanban-task-bridge');
      const cardResults = await KanbanTaskBridge.createCardsFromSubtasks(decomposition.subtasks);

      console.log(`Created ${cardResults.success.length} Kanban cards, ${cardResults.failed.length} failed`);

      // Link Kanban card IDs to subtasks
      for (let i = 0; i < decomposition.subtasks.length; i++) {
        const subtask = decomposition.subtasks[i];
        const correspondingCard = cardResults.success[i];

        if (correspondingCard) {
          // Add Kanban card ID to subtask metadata
          subtask.metadata = {
            ...subtask.metadata,
            kanbanCardId: correspondingCard.id
          };

          // Link task to card for bidirectional reference
          await KanbanTaskBridge.linkTaskToCard(subtask.id, correspondingCard.id, subtask.agent);
        }
      }

      // Set up coordinated task execution with dependency handling
      const { AgentTaskCoordinator } = await import('./agent-task-coordinator');
      const { TaskStatusService } = await import('./task-status-service');

      const taskCoordinator = AgentTaskCoordinator.getInstance(agentManager);
      const statusService = TaskStatusService.getInstance();

      // Set up status service callbacks
      statusService.setCallbacks({
        onStatusUpdate: (update) => {
          console.log(`📊 Status Update: Task ${update.taskId} -> ${update.status}${update.progress ? ` (${update.progress}%)` : ''}`);
          // Update shared state
          sharedState.updateTask(update.taskId, { status: update.status as any });
        },
        onProgressUpdate: (taskId, progress) => {
          console.log(`📈 Progress: Task ${taskId} -> ${progress}%`);
        },
        onKanbanUpdate: (taskId, cardId, status) => {
          console.log(`📋 Kanban: Card ${cardId} for task ${taskId} -> ${status}`);
        }
      });

      // Set up coordination callbacks
      taskCoordinator.setCallbacks({
        onTaskReady: (taskId, agentId) => {
          console.log(`🟢 Task ${taskId} is ready for execution on agent ${agentId}`);
          const subtask = decomposition.subtasks.find(st => st.id === taskId);
          statusService.updateTaskStatus(taskId, agentId, 'pending', {
            message: 'Task ready for execution',
            kanbanCardId: subtask?.metadata?.kanbanCardId
          });
        },
        onTaskStart: (taskId, agentId) => {
          console.log(`🚀 Task ${taskId} started on agent ${agentId}`);
          const subtask = decomposition.subtasks.find(st => st.id === taskId);
          statusService.updateTaskStatus(taskId, agentId, 'running', {
            progress: 10,
            message: 'Task execution started',
            kanbanCardId: subtask?.metadata?.kanbanCardId
          });
        },
        onTaskComplete: async (taskId, agentId, result) => {
          console.log(`✅ Task ${taskId} completed by agent ${agentId}`);
          const subtask = decomposition.subtasks.find(st => st.id === taskId);
          await statusService.reportCompletion(taskId, agentId, result, subtask?.metadata?.kanbanCardId);
        },
        onTaskError: async (taskId, agentId, error, willRetry) => {
          console.error(`❌ Task ${taskId} ${willRetry ? 'failed (will retry)' : 'failed permanently'} on agent ${agentId}: ${error}`);
          const subtask = decomposition.subtasks.find(st => st.id === taskId);
          await statusService.reportError(taskId, agentId, error, willRetry, subtask?.metadata?.kanbanCardId);
        },
        onTaskFailed: async (taskId, agentId, finalError) => {
          console.error(`💀 Task ${taskId} failed permanently on agent ${agentId}: ${finalError}`);
          const subtask = decomposition.subtasks.find(st => st.id === taskId);
          await statusService.updateTaskStatus(taskId, agentId, 'failed', {
            message: `Final failure: ${finalError}`,
            kanbanCardId: subtask?.metadata?.kanbanCardId
          });
        },
        onDependencyResolved: (taskId, dependencyId) => {
          console.log(`🔗 Dependency resolved: Task ${taskId} dependency ${dependencyId} completed`);
        }
      });

      // Register tasks with coordination system
      await taskCoordinator.registerTasks(decomposition.subtasks);

      // Start coordinated execution
      await taskCoordinator.executeCoordinatedTasks(decomposition.subtasks);

      // Add all subtasks to shared state
      for (const subtask of decomposition.subtasks) {
        await sharedState.assignTask({
          agentId: subtask.agent,
          description: subtask.description,
          status: 'pending',
          priority: subtask.priority
        });
      }

      // Get coordination stats
      const coordinationStats = taskCoordinator.getCoordinationStats();
      const statusSummary = statusService.getStatusSummary();

      console.log(`Micromanager orchestration complete with coordination:`);
      console.log(`- Parent Task: ${parentTaskId}`);
      console.log(`- Total Tasks: ${coordinationStats.total}`);
      console.log(`- Kanban Cards: ${cardResults.success.length}`);
      console.log(`- Coordination Status: ${coordinationStats.ready} ready, ${coordinationStats.waiting} waiting for dependencies`);
      console.log(`- Success Rate: ${statusSummary.metrics.successRate.toFixed(1)}%`);

      return parentTaskId;

    } catch (error) {
      console.error('Micromanager task decomposition failed:', error);
      throw error;
    }
  };

  const getSystemHealthColor = (health: number) => {
    if (health >= 80) return 'text-green-500';
    if (health >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getSystemStatusIcon = (health: number) => {
    if (health >= 80) return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (health >= 60) return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    return <AlertTriangle className="h-5 w-5 text-red-500" />;
  };

  return (
    <div className={`h-full bg-background ${className}`}>
      <div className="flex flex-col h-full">
        {/* System Header */}
        <div className="border-b border-border p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                {getSystemStatusIcon(systemMetrics.systemHealthScore || 0)}
                <h1 className="text-2xl font-bold">Agent System</h1>
                <Badge variant={(systemMetrics.systemHealthScore || 0) >= 80 ? 'default' : 'destructive'}>
                  {isNaN(systemMetrics.systemHealthScore) ? '0.0' : (systemMetrics.systemHealthScore || 0).toFixed(1)}% Health
                </Badge>
              </div>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span>{systemMetrics.activeAgents} Active Agents</span>
                <span>{systemMetrics.queueLength} Queued</span>
                <span>{systemMetrics.totalTasks} Total Tasks</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-hidden">
          {showSettings ? (
            <SettingsUI
              settingsManager={settingsManager}
              onClose={() => setShowSettings(false)}
            />
          ) : (
            <Tabs defaultValue="orchestrator" className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-8 mx-4 mt-4">
                <TabsTrigger value="orchestrator">AI Agent Orchestrator</TabsTrigger>
                <TabsTrigger value="agents">Agents</TabsTrigger>
                <TabsTrigger value="tasks">Tasks</TabsTrigger>
                <TabsTrigger value="history">History</TabsTrigger>
                <TabsTrigger value="analytics">Analytics</TabsTrigger>
                <TabsTrigger value="metrics">Metrics</TabsTrigger>
                <TabsTrigger value="optimization">Optimization</TabsTrigger>
                <TabsTrigger value="system">System</TabsTrigger>
              </TabsList>

              <TabsContent value="orchestrator" className="flex-1 p-4 overflow-auto">
                <AgentOrchestratorPanel onTaskSubmit={handleTaskSubmission} />
              </TabsContent>

              <TabsContent value="agents" className="flex-1 p-4 overflow-auto">
                <AgentIntegration />
              </TabsContent>

              <TabsContent value="tasks" className="flex-1 p-4 overflow-auto">
                <TaskManagementPanel />
              </TabsContent>

              <TabsContent value="history" className="flex-1 overflow-hidden">
                <AgentHistoryTab />
              </TabsContent>

              <TabsContent value="analytics" className="flex-1 overflow-hidden">
                <AgentAnalyticsTab />
              </TabsContent>

              <TabsContent value="metrics" className="flex-1 p-4 overflow-auto">
                <MetricsPanel
                  systemMetrics={systemMetrics}
                  agentStatuses={sharedState.agents}
                  agentManager={agentManager}
                />
              </TabsContent>

              <TabsContent value="optimization" className="flex-1 p-4 overflow-auto">
                <OptimizationPanel
                  optimizations={optimizations}
                  agentManager={agentManager}
                />
              </TabsContent>

              <TabsContent value="system" className="flex-1 p-4 overflow-auto">
                <SystemPanel agentManager={agentManager} />
              </TabsContent>
            </Tabs>
          )}
        </div>
      </div>
    </div>
  );
};

// Agent Orchestrator Panel Component
const AgentOrchestratorPanel: React.FC<{ onTaskSubmit: (task: string) => Promise<string> }> = ({ onTaskSubmit }) => {
  const sharedState = useSharedAgentState();
  const [taskInput, setTaskInput] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [lastTaskId, setLastTaskId] = useState<string | null>(null);

  const handleSubmit = async () => {
    if (!taskInput.trim()) return;

    setIsSubmitting(true);
    try {
      const taskId = await onTaskSubmit(taskInput.trim());
      setLastTaskId(taskId);
      setTaskInput(''); // Clear input after successful submission
    } catch (error) {
      console.error('Task submission failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const activeTasks = sharedState.getActiveTasks();
  const recentTasks = sharedState.tasks.slice(-5).reverse();

  // Group tasks by orchestration relationships for status display
  const orchestratorTasks = sharedState.tasks.filter(task =>
    task.description.startsWith('[ORCHESTRATOR]') || task.agentId === 'micromanager'
  );
  const subtasks = sharedState.tasks.filter(task =>
    !task.description.startsWith('[ORCHESTRATOR]') && task.agentId !== 'micromanager'
  );

  return (
    <div className="space-y-6">
      {/* Task Input Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Agent Command Center
          </CardTitle>
          <CardDescription>
            Submit tasks to the AI agent system. The Micromanager will analyze, decompose, and orchestrate execution.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex gap-2">
              <select
                value={sharedState.selectedAgent}
                onChange={(e) => sharedState.setSelectedAgent(e.target.value)}
                className="px-3 py-2 border border-input rounded-md bg-background text-sm min-w-[200px]"
              >
                <option value="micromanager">🤖 Micromanager (Orchestrator)</option>
                <option value="intern">1️⃣ Intern Agent</option>
                <option value="junior">2️⃣ Junior Agent</option>
                <option value="midlevel">3️⃣ MidLevel Agent</option>
                <option value="senior">4️⃣ Senior Agent</option>
                <option value="researcher">📘 Researcher Agent</option>
                <option value="architect">🏗️ Architect Agent</option>
                <option value="designer">🎨 Designer Agent</option>
                <option value="tester">🧪 Tester Agent</option>
              </select>
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || !taskInput.trim()}
                className="min-w-[100px]"
              >
                {isSubmitting ? (
                  <>
                    <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Execute
                  </>
                )}
              </Button>
            </div>
            <Textarea
              placeholder="Describe the task you want the AI agent to perform... (Ctrl+Enter to submit)"
              value={taskInput}
              onChange={(e) => setTaskInput(e.target.value)}
              onKeyDown={handleKeyPress}
              rows={4}
              className="resize-none"
            />
            <div className="text-xs text-muted-foreground">
              💡 Tip: Use Ctrl+Enter to quickly submit tasks. The Micromanager will automatically decompose complex tasks.
            </div>
          </div>

          {lastTaskId && (
            <div className="p-3 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-md">
              <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm font-medium">Task submitted successfully!</span>
              </div>
              <div className="text-xs text-green-600 dark:text-green-400 mt-1">
                Task ID: {lastTaskId}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Status Overview */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Active Tasks</CardTitle>
            <CardDescription>{activeTasks.length} currently executing</CardDescription>
          </CardHeader>
          <CardContent>
            {activeTasks.length === 0 ? (
              <p className="text-sm text-muted-foreground">No active tasks</p>
            ) : (
              <div className="space-y-2">
                {activeTasks.slice(0, 3).map(task => (
                  <div key={task.id} className="p-2 border rounded-md">
                    <div className="font-medium text-sm truncate">{task.description}</div>
                    <div className="text-xs text-muted-foreground">
                      Agent: {task.agentId} | Priority: {task.priority}
                    </div>
                  </div>
                ))}
                {activeTasks.length > 3 && (
                  <div className="text-xs text-muted-foreground text-center">
                    +{activeTasks.length - 3} more tasks
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Recent Tasks</CardTitle>
            <CardDescription>Last {recentTasks.length} submitted</CardDescription>
          </CardHeader>
          <CardContent>
            {recentTasks.length === 0 ? (
              <p className="text-sm text-muted-foreground">No recent tasks</p>
            ) : (
              <div className="space-y-2">
                {recentTasks.slice(0, 3).map(task => (
                  <div key={task.id} className="p-2 border rounded-md">
                    <div className="font-medium text-sm truncate">{task.description}</div>
                    <div className="text-xs text-muted-foreground flex items-center justify-between">
                      <span>Agent: {task.agentId}</span>
                      <Badge variant={
                        task.status === 'completed' ? 'default' :
                        task.status === 'failed' ? 'destructive' :
                        task.status === 'running' ? 'secondary' : 'outline'
                      }>
                        {task.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base">System Status</CardTitle>
            <CardDescription>Agent system & Kanban integration</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Active Agents</span>
                <span className="font-medium">
                  {sharedState.agents.filter(a => a.status === 'busy' || a.status === 'idle').length}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Queue Length</span>
                <span className="font-medium">
                  {sharedState.tasks.filter(t => t.status === 'pending').length}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Success Rate</span>
                <span className="font-medium text-green-600">
                  {sharedState.tasks.length > 0 ?
                    Math.round((sharedState.tasks.filter(t => t.status === 'completed').length / sharedState.tasks.length) * 100) : 0
                  }%
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>📋 Kanban Cards</span>
                <span className="font-medium text-blue-600">
                  {subtasks.length} linked
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>🤖 Orchestrations</span>
                <span className="font-medium text-purple-600">
                  {orchestratorTasks.length} active
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>🚀 Dispatched Tasks</span>
                <span className="font-medium text-orange-600">
                  {activeTasks.length} executing
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>🔗 Dependencies</span>
                <span className="font-medium text-blue-600">
                  Coordinated
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span>📊 Success Rate</span>
                <span className="font-medium text-green-600">
                  Real-time
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

// Task Management Panel Component
const TaskManagementPanel: React.FC = () => {
  const sharedState = useSharedAgentState();

  const activeTasks = sharedState.tasks.filter(task => task.status === 'running');
  const queuedTasks = sharedState.tasks.filter(task => task.status === 'pending');
  const taskHistory = sharedState.tasks.filter(task => task.status === 'completed' || task.status === 'failed').slice(-20);

  // Group tasks by orchestration relationships
  const orchestratorTasks = sharedState.tasks.filter(task =>
    task.description.startsWith('[ORCHESTRATOR]') || task.agentId === 'micromanager'
  );
  const subtasks = sharedState.tasks.filter(task =>
    !task.description.startsWith('[ORCHESTRATOR]') && task.agentId !== 'micromanager'
  );

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getTaskTypeIcon = (agentId: string, description: string) => {
    if (description.startsWith('[ORCHESTRATOR]')) return '🤖';
    switch (agentId) {
      case 'designer': return '🎨';
      case 'architect': return '🏗️';
      case 'researcher': return '📘';
      case 'senior': return '4️⃣';
      case 'midlevel': return '3️⃣';
      case 'junior': return '2️⃣';
      case 'intern': return '1️⃣';
      case 'tester': return '🧪';
      default: return '⚡';
    }
  };

  return (
    <div className="space-y-6">
      {/* Orchestration Overview */}
      {orchestratorTasks.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              🤖 Task Orchestration Overview
            </CardTitle>
            <CardDescription>
              {orchestratorTasks.length} orchestration task(s) managing {subtasks.length} subtask(s)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {orchestratorTasks.slice(0, 3).map(orchestratorTask => {
                const relatedSubtasks = subtasks.filter(st =>
                  Math.abs(st.createdAt - orchestratorTask.createdAt) < 5000 // Within 5 seconds
                );

                return (
                  <div key={orchestratorTask.id} className="border rounded-md p-3">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="text-sm">🤖</span>
                          <div className="font-medium text-sm">
                            {orchestratorTask.description.replace('[ORCHESTRATOR] ', '')}
                          </div>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          Micromanager | Priority: {orchestratorTask.priority}
                        </div>
                      </div>
                      <Badge variant={
                        orchestratorTask.status === 'completed' ? 'default' :
                        orchestratorTask.status === 'failed' ? 'destructive' :
                        orchestratorTask.status === 'running' ? 'secondary' : 'outline'
                      } className="text-xs">
                        {orchestratorTask.status}
                      </Badge>
                    </div>

                    {relatedSubtasks.length > 0 && (
                      <div className="mt-3 pl-4 border-l-2 border-muted">
                        <div className="text-xs font-medium text-muted-foreground mb-2">
                          Subtasks ({relatedSubtasks.length}):
                        </div>
                        <div className="space-y-1">
                          {relatedSubtasks.slice(0, 4).map(subtask => (
                            <div key={subtask.id} className="flex items-center gap-2 text-xs">
                              <span>{getTaskTypeIcon(subtask.agentId, subtask.description)}</span>
                              <span className="truncate flex-1">{subtask.description}</span>
                              <div className="flex items-center gap-1">
                                <Badge variant="outline" className="text-xs">
                                  {subtask.status}
                                </Badge>
                                <span className="text-xs text-muted-foreground">📋</span>
                              </div>
                            </div>
                          ))}
                          {relatedSubtasks.length > 4 && (
                            <div className="text-xs text-muted-foreground">
                              +{relatedSubtasks.length - 4} more subtasks
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Active Tasks</CardTitle>
            <CardDescription>{activeTasks.length} currently executing</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {activeTasks.slice(0, 5).map(task => (
                <div key={task.id} className="p-3 border rounded-md">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="font-medium text-sm truncate">{task.description}</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        Agent: {task.agentId} | Priority: {task.priority}
                      </div>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {task.status}
                    </Badge>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Created: {formatTimestamp(task.createdAt)}
                  </div>
                </div>
              ))}
              {activeTasks.length === 0 && (
                <p className="text-sm text-muted-foreground">No active tasks</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Queued Tasks</CardTitle>
            <CardDescription>{queuedTasks.length} waiting for execution</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {queuedTasks.slice(0, 5).map(task => (
                <div key={task.id} className="p-3 border rounded-md">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="text-sm">{getTaskTypeIcon(task.agentId, task.description)}</span>
                        <div className="font-medium text-sm truncate">{task.description}</div>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        Agent: {task.agentId} | Priority: {task.priority}
                      </div>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {task.status}
                    </Badge>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Queued: {formatTimestamp(task.createdAt)}
                  </div>
                </div>
              ))}
              {queuedTasks.length === 0 && (
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground">No queued tasks</p>
                  <p className="text-xs text-muted-foreground mt-1">Submit a task to see it here</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Recent History</CardTitle>
            <CardDescription>Last {taskHistory.length} completed</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {taskHistory.slice(0, 5).map(task => (
                <div key={task.id} className="p-2 border rounded-md">
                  <div className="font-medium text-sm truncate">{task.description}</div>
                  <div className="text-xs text-muted-foreground flex items-center justify-between">
                    <span>Agent: {task.agentId}</span>
                    <Badge variant={task.status === 'completed' ? 'default' : 'destructive'}>
                      {task.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

// Metrics Panel Component
const MetricsPanel: React.FC<{
  systemMetrics: any;
  agentStatuses: any[];
  agentManager: CompleteAgentManager;
}> = ({ systemMetrics, agentStatuses, agentManager }) => {
  const successRate = (systemMetrics.totalTasks || 0) > 0 ?
    ((systemMetrics.successfulTasks || 0) / (systemMetrics.totalTasks || 1)) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* System Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart className="h-5 w-5" />
            System Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="space-y-2">
              <div className="text-sm font-medium">Success Rate</div>
              <div className="text-2xl font-bold text-green-600">{isNaN(successRate) ? '0.0' : successRate.toFixed(1)}%</div>
              <Progress value={isNaN(successRate) ? 0 : successRate} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Average Response Time</div>
              <div className="text-2xl font-bold">{((systemMetrics.averageResponseTime || 0) / 1000).toFixed(1)}s</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Total Tokens</div>
              <div className="text-2xl font-bold">{(systemMetrics.totalTokensUsed || 0).toLocaleString()}</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">System Health</div>
              <div className="text-2xl font-bold text-blue-600">{isNaN(systemMetrics.systemHealthScore) ? '0.0' : (systemMetrics.systemHealthScore || 0).toFixed(1)}%</div>
              <Progress value={isNaN(systemMetrics.systemHealthScore) ? 0 : (systemMetrics.systemHealthScore || 0)} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Agent Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Agent Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {agentStatuses.map(agent => (
              <div key={agent.id} className="p-4 border rounded-md">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">{agent.name}</span>
                  <Badge variant={(agent.healthScore || 0) >= 70 ? 'default' : 'destructive'}>
                    {isNaN(agent.healthScore) ? '0' : (agent.healthScore || 0).toFixed(0)}%
                  </Badge>
                </div>
                <Progress value={isNaN(agent.healthScore) ? 0 : (agent.healthScore || 0)} className="h-2 mb-2" />
                <div className="grid grid-cols-2 gap-2 text-sm text-muted-foreground">
                  <div>Tasks: {isNaN(agent.tasksCompleted) ? '0' : (agent.tasksCompleted || 0).toString()}</div>
                  <div>Errors: {isNaN(agent.errorCount) ? '0' : (agent.errorCount || 0).toString()}</div>
                  <div>Tokens: {isNaN(agent.tokensUsed) ? '0' : (agent.tokensUsed || 0).toLocaleString()}</div>
                  <div>Status: {agent.status}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Optimization Panel Component
const OptimizationPanel: React.FC<{
  optimizations: any[];
  agentManager: CompleteAgentManager;
}> = ({ optimizations, agentManager }) => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Optimization Suggestions</CardTitle>
          <CardDescription>AI-generated recommendations to improve system performance</CardDescription>
        </CardHeader>
        <CardContent>
          {optimizations.length > 0 ? (
            <div className="space-y-4">
              {optimizations.map(opt => (
                <div key={opt.id} className="p-4 border rounded-md">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant={opt.priority === 'high' ? 'destructive' : opt.priority === 'medium' ? 'default' : 'secondary'}>
                        {opt.priority}
                      </Badge>
                      <span className="font-medium">{opt.targetAgent}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Impact: {isNaN(opt.expectedImpact) ? '0' : (opt.expectedImpact * 100).toFixed(0)}% | Effort: {opt.effort || 0}/10
                    </div>
                  </div>
                  <p className="text-sm">{opt.description}</p>
                  {opt.data?.suggestions && (
                    <div className="mt-2">
                      <div className="text-xs font-medium text-muted-foreground mb-1">Suggestions:</div>
                      <ul className="text-xs text-muted-foreground list-disc list-inside">
                        {opt.data.suggestions.map((suggestion: string, index: number) => (
                          <li key={index}>{suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">No optimization suggestions available</p>
          )}
        </CardContent>
      </Card>

      {/* Learning Patterns */}
      <Card>
        <CardHeader>
          <CardTitle>Learning Patterns</CardTitle>
          <CardDescription>Patterns identified by the learning system</CardDescription>
        </CardHeader>
        <CardContent>
          <LearningPatternsDisplay agentManager={agentManager} />
        </CardContent>
      </Card>
    </div>
  );
};

// Learning Patterns Display Component
const LearningPatternsDisplay: React.FC<{ agentManager: CompleteAgentManager }> = ({ agentManager }) => {
  const [patterns, setPatterns] = useState(agentManager.getLearningPatterns());
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    const interval = setInterval(() => {
      setPatterns(agentManager.getLearningPatterns());
    }, 10000);

    return () => clearInterval(interval);
  }, [agentManager]);

  const categories = ['all', 'success', 'failure', 'optimization', 'error_resolution'];
  const filteredPatterns = selectedCategory === 'all' ?
    patterns : patterns.filter(p => p.category === selectedCategory);

  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        {categories.map(category => (
          <Button
            key={category}
            variant={selectedCategory === category ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory(category)}
          >
            {category.charAt(0).toUpperCase() + category.slice(1)}
          </Button>
        ))}
      </div>

      <div className="space-y-3 max-h-64 overflow-auto">
        {filteredPatterns.slice(0, 10).map(pattern => (
          <div key={pattern.id} className="p-3 border rounded-md">
            <div className="flex items-center justify-between mb-1">
              <Badge variant={
                pattern.category === 'success' ? 'default' :
                pattern.category === 'failure' ? 'destructive' :
                'secondary'
              }>
                {pattern.category}
              </Badge>
              <div className="text-xs text-muted-foreground">
                Frequency: {pattern.frequency || 0} | Effectiveness: {isNaN(pattern.effectiveness) ? '0' : (pattern.effectiveness * 100).toFixed(0)}%
              </div>
            </div>
            <p className="text-sm font-medium mb-1">{pattern.pattern}</p>
            {pattern.recommendations.length > 0 && (
              <div className="text-xs text-muted-foreground">
                <strong>Recommendations:</strong> {pattern.recommendations.slice(0, 2).join(', ')}
              </div>
            )}
          </div>
        ))}
        {filteredPatterns.length === 0 && (
          <p className="text-sm text-muted-foreground">No patterns found for this category</p>
        )}
      </div>
    </div>
  );
};

// System Panel Component
const SystemPanel: React.FC<{ agentManager: CompleteAgentManager }> = ({ agentManager }) => {
  const [systemReport, setSystemReport] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);

  const generateReport = async () => {
    setIsGenerating(true);
    try {
      const report = await agentManager.generateSystemReport();
      setSystemReport(report);
    } catch (error) {
      console.error('Failed to generate system report:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            System Status
          </CardTitle>
          <CardDescription>Overall system health and diagnostics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button onClick={generateReport} disabled={isGenerating}>
              {isGenerating ? 'Generating...' : 'Generate System Report'}
            </Button>

            {systemReport && (
              <div className="bg-muted p-4 rounded-md">
                <pre className="text-sm whitespace-pre-wrap font-mono">{systemReport}</pre>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* System Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>System Configuration</CardTitle>
          <CardDescription>Current system configuration and capabilities</CardDescription>
        </CardHeader>
        <CardContent>
          <SystemConfigurationDisplay agentManager={agentManager} />
        </CardContent>
      </Card>
    </div>
  );
};

// System Configuration Display Component
const SystemConfigurationDisplay: React.FC<{ agentManager: CompleteAgentManager }> = ({ agentManager }) => {
  const agents = agentManager.getAgents();
  const agentsByType = agents.reduce((acc, agent) => {
    const type = agent.getType();
    if (!acc[type]) acc[type] = [];
    acc[type].push(agent);
    return acc;
  }, {} as Record<string, any[]>);

  return (
    <div className="space-y-4">
      {Object.entries(agentsByType).map(([type, typeAgents]) => (
        <div key={type} className="space-y-2">
          <h3 className="font-medium capitalize">{type} Agents ({typeAgents.length})</h3>
          <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
            {typeAgents.map(agent => (
              <div key={agent.getId()} className="p-3 border rounded-md">
                <div className="font-medium text-sm">{agent.getName()}</div>
                <div className="text-xs text-muted-foreground mt-1">
                  Capabilities: {agent.getCapabilities().slice(0, 3).join(', ')}
                  {agent.getCapabilities().length > 3 && ` +${agent.getCapabilities().length - 3} more`}
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

// Integration Instructions Component
export const IntegrationInstructions: React.FC = () => {
  return (
    <Card className="m-4">
      <CardHeader>
        <CardTitle>🚀 Agent System Integration Instructions</CardTitle>
        <CardDescription>How to integrate the complete agent system into your application</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-md">
          <h3 className="font-semibold mb-2">📋 Integration Checklist</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ All agent implementations completed</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Middleware components implemented</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Complete Agent Manager with orchestration</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Settings management system</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Health monitoring and error resolution</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Continuous learning system</span>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 dark:bg-yellow-950 p-4 rounded-md">
          <h3 className="font-semibold mb-2">⚠️ Integration Steps</h3>
          <ol className="text-sm space-y-2 list-decimal list-inside">
            <li>Copy all agent files to <code>components/agents/</code></li>
            <li>Copy middleware files to <code>components/middleware/</code></li>
            <li>Copy settings files to <code>components/settings/</code></li>
            <li>Update <code>components/agents/index.ts</code> with all exports</li>
            <li>Replace existing AgentManager with CompleteAgentManager</li>
            <li>Update main application to use CompleteAgentSystem component</li>
            <li>Configure API keys in settings</li>
            <li>Test system functionality with sample tasks</li>
          </ol>
        </div>

        <div className="bg-green-50 dark:bg-green-950 p-4 rounded-md">
          <h3 className="font-semibold mb-2">🎯 Usage Example</h3>
          <pre className="text-sm bg-background p-3 rounded border mt-2 overflow-auto">
{`// In your main application component
import { CompleteAgentSystem } from '@/components/agents/complete-integration';

export default function App() {
  return (
    <div className="h-screen">
      <CompleteAgentSystem />
    </div>
  );
}

// To submit tasks programmatically
const agentManager = new CompleteAgentManager();
const taskId = await agentManager.submitTask(
  "Create a React component for user authentication",
  ["auth.tsx", "types.ts"],
  "high"
);`}
          </pre>
        </div>

        <div className="bg-purple-50 dark:bg-purple-950 p-4 rounded-md">
          <h3 className="font-semibold mb-2">🔧 Key Features Available</h3>
          <ul className="text-sm space-y-1 list-disc list-inside">
            <li>🤖 9 specialized AI agents (Intern → Senior + Specialized)</li>
            <li>🧠 Intelligent task classification and routing</li>
            <li>📊 Real-time health monitoring and performance metrics</li>
            <li>🔄 Automatic error resolution with escalation</li>
            <li>📈 Continuous learning and optimization</li>
            <li>⚙️ Comprehensive settings management</li>
            <li>💰 Cost tracking and resource optimization</li>
            <li>🔒 Privacy-first architecture with local processing</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

// Main export
export default CompleteAgentSystem;