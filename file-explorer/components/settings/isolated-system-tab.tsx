// components/settings/isolated-system-tab.tsx
import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Save } from 'lucide-react';
import { SettingsManager, SystemSettings } from './settings-manager';
import { CompactStatusIndicator, SettingStatus } from './setting-status-indicator';
import { getSettingStatus, logStatusValidation } from './system-status-map';

interface IsolatedSystemTabProps {
  settings: SystemSettings;
  updateSystemSettings: (updates: Partial<SystemSettings>) => void;
}

/**
 * ✅ Isolated System Tab Component
 * Uses local state for immediate UI feedback
 * Follows the same pattern as the working Agents tab
 */
export const IsolatedSystemTab = React.memo<IsolatedSystemTabProps>(({
  settings,
  updateSystemSettings
}) => {
  // ✅ Local state for immediate UI feedback
  const [localSettings, setLocalSettings] = useState<SystemSettings>(settings);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    console.log('🔄 IsolatedSystemTab rendered');
    // ✅ Log status validation for development verification
    logStatusValidation();
  });

  // ✅ Sync local state when global settings change
  useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  // ✅ Immediate toggle handler with local state
  const handleToggle = useCallback((key: keyof SystemSettings) => {
    console.time('system-toggle-latency');
    const newValue = !localSettings[key];
    setLocalSettings(prev => ({
      ...prev,
      [key]: newValue
    }));
    updateSystemSettings({ [key]: newValue });
    console.timeEnd('system-toggle-latency');
  }, [localSettings, updateSystemSettings]);

  // ✅ Individual slider handlers (matching Agents tab pattern)
  const handleAutoSaveIntervalChange = useCallback((value: number) => {
    console.time('system-slider-latency');
    setLocalSettings(prev => ({ ...prev, autoSaveInterval: value }));
    console.timeEnd('system-slider-latency');
  }, []);

  const handleMaxTasksChange = useCallback((value: number) => {
    console.time('system-slider-latency');
    setLocalSettings(prev => ({ ...prev, maxConcurrentTasks: value }));
    console.timeEnd('system-slider-latency');
  }, []);

  // ✅ Default timeout slider handler
  const handleDefaultTimeoutChange = useCallback((value: number) => {
    console.time('system-slider-latency');
    setLocalSettings(prev => ({ ...prev, defaultTimeout: value }));
    console.timeEnd('system-slider-latency');
  }, []);

  // ✅ Theme selector handler
  const handleThemeChange = useCallback((value: string) => {
    console.time('system-theme-latency');
    const theme = value as 'light' | 'dark' | 'system';

    // ✅ Prevent update unless the selected value differs
    if (theme !== localSettings.theme) {
      setLocalSettings(prev => ({ ...prev, theme }));
      updateSystemSettings({ theme });
      console.log(`🎨 Theme changed: ${localSettings.theme} → ${theme}`);
    }
    console.timeEnd('system-theme-latency');
  }, [localSettings.theme, updateSystemSettings]);

  // ✅ Commit handlers (matching Agents tab pattern)
  const commitAutoSaveInterval = useCallback(() => {
    console.time('system-slider-commit');
    updateSystemSettings({ autoSaveInterval: localSettings.autoSaveInterval });
    console.timeEnd('system-slider-commit');
  }, [localSettings.autoSaveInterval, updateSystemSettings]);

  const commitMaxTasks = useCallback(() => {
    console.time('system-slider-commit');
    updateSystemSettings({ maxConcurrentTasks: localSettings.maxConcurrentTasks });
    console.timeEnd('system-slider-commit');
  }, [localSettings.maxConcurrentTasks, updateSystemSettings]);

  const commitDefaultTimeout = useCallback(() => {
    console.time('system-slider-commit');
    updateSystemSettings({ defaultTimeout: localSettings.defaultTimeout });
    console.timeEnd('system-slider-commit');
  }, [localSettings.defaultTimeout, updateSystemSettings]);

  // ✅ Manual save handler
  const handleManualSave = useCallback(async () => {
    setIsSaving(true);
    try {
      // Import AutoSaveService dynamically to avoid circular dependencies
      const { AutoSaveService } = await import('../auto-save/auto-save-service');
      const autoSaveService = AutoSaveService.getInstance();
      await autoSaveService.triggerManualSave();
      console.log('✅ Manual save completed');
    } catch (error) {
      console.error('❌ Manual save failed:', error);
    } finally {
      setIsSaving(false);
    }
  }, []);

  // ✅ Memoized toggle component with status indicator
  const SystemToggle = React.memo(({
    id,
    label,
    settingKey,
    description
  }: {
    id: string;
    label: string;
    settingKey: keyof SystemSettings;
    description?: string;
  }) => {
    const descriptionId = description ? `${id}-desc` : undefined;
    const statusInfo = getSettingStatus(settingKey);

    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Label htmlFor={id}>{label}</Label>
            <CompactStatusIndicator
              status={statusInfo.status}
              statusLabel={statusInfo.statusLabel}
            />
          </div>
          <Switch
            id={id}
            checked={localSettings[settingKey] as boolean}
            onCheckedChange={() => handleToggle(settingKey)}
            aria-label={label}
            aria-describedby={descriptionId}
          />
        </div>
        {description && (
          <div id={descriptionId} className="text-sm text-muted-foreground">
            {description}
          </div>
        )}
      </div>
    );
  });

  // ✅ Memoized slider components with status indicators (matching Agents tab pattern)
  const AutoSaveIntervalSlider = React.memo(() => {
    const statusInfo = getSettingStatus('autoSaveInterval');

    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Label>Auto Save Interval (seconds)</Label>
          <CompactStatusIndicator
            status={statusInfo.status}
            statusLabel={statusInfo.statusLabel}
          />
        </div>
        <Slider
          value={[localSettings.autoSaveInterval]}
          onValueChange={([value]) => handleAutoSaveIntervalChange(value)}
          onPointerUp={commitAutoSaveInterval}
          min={10}
          max={300}
          step={10}
          aria-label="Auto Save Interval"
          aria-valuetext={`${localSettings.autoSaveInterval} seconds`}
        />
        <div className="text-sm text-muted-foreground">
          {localSettings.autoSaveInterval} seconds
        </div>
      </div>
    );
  });

  const MaxTasksSlider = React.memo(() => {
    const statusInfo = getSettingStatus('maxConcurrentTasks');

    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Label>Max Concurrent Tasks</Label>
          <CompactStatusIndicator
            status={statusInfo.status}
            statusLabel={statusInfo.statusLabel}
          />
        </div>
        <Slider
          value={[localSettings.maxConcurrentTasks]}
          onValueChange={([value]) => handleMaxTasksChange(value)}
          onPointerUp={commitMaxTasks}
          min={1}
          max={20}
          step={1}
          aria-label="Max Concurrent Tasks"
          aria-valuetext={`${localSettings.maxConcurrentTasks} tasks`}
        />
        <div className="text-sm text-muted-foreground">
          {localSettings.maxConcurrentTasks} tasks
        </div>
      </div>
    );
  });

  // ✅ Memoized default timeout slider component with status indicator
  const DefaultTimeoutSlider = React.memo(() => {
    const statusInfo = getSettingStatus('defaultTimeout');

    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Label>Default Timeout</Label>
          <CompactStatusIndicator
            status={statusInfo.status}
            statusLabel={statusInfo.statusLabel}
          />
        </div>
        <Slider
          value={[localSettings.defaultTimeout]}
          onValueChange={([value]) => handleDefaultTimeoutChange(value)}
          onPointerUp={commitDefaultTimeout}
          min={5000}
          max={120000}
          step={5000}
          aria-label="Default Timeout"
          aria-valuetext={`${localSettings.defaultTimeout / 1000} seconds`}
        />
        <div className="text-sm text-muted-foreground">
          {localSettings.defaultTimeout / 1000} seconds
        </div>
      </div>
    );
  });

  // ✅ Memoized theme selector component with status indicator
  const ThemeSelector = React.memo(() => {
    const statusInfo = getSettingStatus('theme');

    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Label htmlFor="theme-selector">Theme</Label>
          <CompactStatusIndicator
            status={statusInfo.status}
            statusLabel={statusInfo.statusLabel}
          />
        </div>
        <Select value={localSettings.theme} onValueChange={handleThemeChange}>
          <SelectTrigger id="theme-selector" aria-label={`Theme Selector, currently set to: ${localSettings.theme}`}>
            <SelectValue placeholder="Select theme" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="light">Light</SelectItem>
            <SelectItem value="dark">Dark</SelectItem>
            <SelectItem value="system">System Default</SelectItem>
          </SelectContent>
        </Select>
      </div>
    );
  });

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>System Settings</CardTitle>
          <CardDescription>Configure global application behavior</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <ThemeSelector />

          <SystemToggle
            id="auto-save"
            label="Enable Auto Save"
            settingKey="autoSave"
          />

          <AutoSaveIntervalSlider />

          {/* ✅ Manual Save Button */}
          <div className="space-y-2">
            <Label>Manual Save</Label>
            <Button
              onClick={handleManualSave}
              disabled={isSaving}
              variant="outline"
              className="w-full"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save All Data Now'}
            </Button>
            <div className="text-sm text-muted-foreground">
              Manually trigger a save of all application data (settings, agents, editor content, kanban boards)
            </div>
          </div>

          <MaxTasksSlider />

          <DefaultTimeoutSlider />

          <SystemToggle
            id="enable-telemetry"
            label="Enable Telemetry"
            settingKey="enableTelemetry"
            description="Allow anonymous usage analytics to help improve the product (no personal data collected)."
          />

          <SystemToggle
            id="debug-mode"
            label="Enable Debug Mode"
            settingKey="debugMode"
          />
        </CardContent>
      </Card>
    </div>
  );
});

IsolatedSystemTab.displayName = 'IsolatedSystemTab';
