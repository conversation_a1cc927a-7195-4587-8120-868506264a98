// components/settings/system-status-map.ts
import { SettingStatus } from './setting-status-indicator';
import { SystemSettings } from './settings-manager';

export interface SettingStatusInfo {
  status: SettingStatus;
  statusLabel: string;
  integrationDetails: string;
}

/**
 * ✅ System Settings Status Mapping
 * Hard-mapped based on real runtime evidence from codebase analysis
 * Each status is verified by actual integration points, not guessed
 */
export const SYSTEM_SETTINGS_STATUS_MAP: Record<keyof SystemSettings, SettingStatusInfo> = {
  theme: {
    status: 'active',
    statusLabel: 'Connected to ThemeBridge component and next-themes system',
    integrationDetails: 'Used by: ThemeBridge.tsx, next-themes provider, immediate UI theme changes'
  },

  autoSave: {
    status: 'active', 
    statusLabel: 'Controls AutoSaveEngine timer and AutoSaveProvider behavior',
    integrationDetails: 'Used by: AutoSaveEngine.useAutoSave(), AutoSaveProvider, periodic data persistence'
  },

  autoSaveInterval: {
    status: 'active',
    statusLabel: 'Sets timing interval for AutoSaveEngine operations',
    integrationDetails: 'Used by: AutoSaveEngine timer (line 49-52), converted to milliseconds for setInterval'
  },

  maxConcurrentTasks: {
    status: 'active',
    statusLabel: 'Enforced by ConcurrencyManager and agent system task limits',
    integrationDetails: 'Used by: ConcurrencyManager.setLimit(), CompleteAgentManager.updateConcurrencyLimit(), useConcurrency hook'
  },

  defaultTimeout: {
    status: 'active',
    statusLabel: 'Applied to all timeout-aware operations and API calls',
    integrationDetails: 'Used by: useTimeout hook, useTimeoutAPI, agent operations, LLM calls, fetchWithTimeout utilities'
  },

  enableTelemetry: {
    status: 'active',
    statusLabel: 'Controls privacy-aware telemetry collection system-wide',
    integrationDetails: 'Used by: telemetry.ts trackEvent(), useTelemetry hook, Electron telemetry sync, event queuing'
  },

  debugMode: {
    status: 'active',
    statusLabel: 'Enables debug logging throughout application and Electron process',
    integrationDetails: 'Used by: debug.ts debugLog(), useDebug hook, Electron IPC sync, console.debug output control'
  }
};

/**
 * ✅ Get status information for a specific setting
 * Returns verified integration status based on real codebase analysis
 */
export function getSettingStatus(settingKey: keyof SystemSettings): SettingStatusInfo {
  const statusInfo = SYSTEM_SETTINGS_STATUS_MAP[settingKey];
  
  if (!statusInfo) {
    // Fallback for unknown settings (should not happen with current codebase)
    console.warn(`⚠️ Unknown setting status requested: ${settingKey}`);
    return {
      status: 'broken',
      statusLabel: 'Unknown setting - status verification needed',
      integrationDetails: 'Setting not found in status map'
    };
  }

  return statusInfo;
}

/**
 * ✅ Get all settings with their status information
 * Useful for debugging and status overview
 */
export function getAllSettingsStatus(): Record<keyof SystemSettings, SettingStatusInfo> {
  return { ...SYSTEM_SETTINGS_STATUS_MAP };
}

/**
 * ✅ Validate status mapping completeness
 * Ensures all SystemSettings keys have status mappings
 */
export function validateStatusMapping(): { isComplete: boolean; missingKeys: string[] } {
  // Get all keys from SystemSettings interface (runtime check)
  const defaultSettings: SystemSettings = {
    theme: 'system',
    autoSave: true,
    autoSaveInterval: 30,
    maxConcurrentTasks: 5,
    defaultTimeout: 30000,
    enableTelemetry: false,
    debugMode: false
  };

  const settingKeys = Object.keys(defaultSettings) as (keyof SystemSettings)[];
  const statusKeys = Object.keys(SYSTEM_SETTINGS_STATUS_MAP) as (keyof SystemSettings)[];
  
  const missingKeys = settingKeys.filter(key => !statusKeys.includes(key));
  
  return {
    isComplete: missingKeys.length === 0,
    missingKeys
  };
}

/**
 * ✅ Log status mapping validation (for development)
 * Call this during development to verify mapping completeness
 */
export function logStatusValidation(): void {
  const validation = validateStatusMapping();
  
  if (validation.isComplete) {
    console.log('✅ System settings status mapping is complete');
  } else {
    console.warn('⚠️ Missing status mappings for:', validation.missingKeys);
  }
  
  // Log all current mappings
  const allStatuses = getAllSettingsStatus();
  console.table(
    Object.entries(allStatuses).map(([key, info]) => ({
      Setting: key,
      Status: info.status,
      Integration: info.integrationDetails
    }))
  );
}
