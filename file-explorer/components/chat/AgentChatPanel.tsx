"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON>, Send, User, Clock, CheckCircle, RefreshCw } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useAgentChat, type AgentMessage } from "@/hooks/useAgentChat"

type MessageRole = "user" | "agent" | "system"
type MessageStatus = "sending" | "sent" | "processing" | "completed" | "error"

interface AgentChatPanelProps {
  onClose: () => void
}

export default function AgentChatPanel({ onClose }: AgentChatPanelProps) {
  const [input, setInput] = useState("")
  const { messages, isProcessing, sendMessage } = useAgentChat()
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const handleSendMessage = async () => {
    if (!input.trim()) return

    await sendMessage(input)
    setInput("")
  }

  const handleRetryMessage = async (content: string) => {
    await sendMessage(content)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  const getMessageIcon = (role: MessageRole, agentType?: string) => {
    if (role === "user") return <User className="h-4 w-4" />
    if (role === "agent") return <Bot className="h-4 w-4" />
    return <Bot className="h-4 w-4" />
  }

  const getStatusIcon = (status: MessageStatus) => {
    switch (status) {
      case "sending":
        return <Clock className="h-3 w-3 animate-spin" />
      case "processing":
        return <Clock className="h-3 w-3 animate-pulse" />
      case "completed":
        return <CheckCircle className="h-3 w-3 text-green-600" />
      case "error":
        return <CheckCircle className="h-3 w-3 text-red-600" />
      default:
        return null
    }
  }

  const getAgentBadgeColor = (agentType?: string) => {
    switch (agentType) {
      case "micromanager":
        return "bg-blue-500/10 text-blue-600 border-blue-500/20"
      case "designer":
        return "bg-purple-500/10 text-purple-600 border-purple-500/20"
      case "developer":
        return "bg-green-500/10 text-green-600 border-green-500/20"
      case "tester":
        return "bg-orange-500/10 text-orange-600 border-orange-500/20"
      default:
        return "bg-gray-500/10 text-gray-600 border-gray-500/20"
    }
  }

  return (
    <Card className="flex flex-col h-full border-0 rounded-none bg-background">
      <CardHeader className="border-b border-editor-border p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bot className="h-4 w-4 text-editor-highlight" />
            <span className="font-medium text-sm">Agent Chat</span>
            <Badge variant="outline" className="text-xs">
              Micromanager
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden p-0">
        <ScrollArea className="h-full">
          <div className="p-3 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  "flex gap-3",
                  message.role === "user" ? "justify-end" : "justify-start"
                )}
              >
                {message.role !== "user" && (
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-accent flex items-center justify-center">
                    {getMessageIcon(message.role, message.agentType)}
                  </div>
                )}

                <div className={cn(
                  "max-w-[80%] space-y-1",
                  message.role === "user" ? "items-end" : "items-start"
                )}>
                  <div className={cn(
                    "rounded-lg px-3 py-2 text-sm",
                    message.role === "user"
                      ? "bg-editor-highlight text-editor-highlight-fg"
                      : message.role === "system"
                      ? "bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-800"
                      : message.status === "error"
                      ? "bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-800"
                      : "bg-accent text-accent-foreground"
                  )}>
                    {message.content}
                  </div>

                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    {message.agentType && (
                      <Badge
                        variant="outline"
                        className={cn("text-xs h-4", getAgentBadgeColor(message.agentType))}
                      >
                        {message.agentType}
                      </Badge>
                    )}
                    <span>{message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}</span>
                    {getStatusIcon(message.status)}
                    {message.status === "error" && message.role === "system" && (
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-4 px-1 text-xs"
                        onClick={() => {
                          // Extract original user message from error content
                          const match = message.content.match(/failed to respond: (.+)/)
                          if (match) {
                            const originalContent = match[1]
                            handleRetryMessage(originalContent)
                          }
                        }}
                      >
                        <RefreshCw className="h-3 w-3 mr-1" />
                        Retry
                      </Button>
                    )}
                  </div>
                </div>

                {message.role === "user" && (
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-editor-highlight flex items-center justify-center">
                    {getMessageIcon(message.role)}
                  </div>
                )}
              </div>
            ))}

            {isProcessing && (
              <div className="flex gap-3 justify-start">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-accent flex items-center justify-center">
                  <Bot className="h-4 w-4" />
                </div>
                <div className="bg-accent text-accent-foreground rounded-lg px-3 py-2 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                      <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                      <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
                    </div>
                    <span>Agent is thinking...</span>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </CardContent>

      <CardFooter className="border-t border-editor-border p-3">
        <div className="flex items-center w-full gap-2">
          <Input
            placeholder="Describe what you'd like to accomplish..."
            className="flex-1 bg-background border-input text-sm"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyPress}
            disabled={isProcessing}
          />
          <Button
            size="icon"
            className="bg-editor-highlight text-editor-highlight-fg hover:bg-editor-highlight/90"
            onClick={handleSendMessage}
            disabled={!input.trim() || isProcessing}
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
