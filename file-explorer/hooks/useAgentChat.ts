"use client"

import { useState, useCallback } from "react"

type MessageRole = "user" | "agent" | "system"
type MessageStatus = "sending" | "sent" | "processing" | "completed" | "error"

export type AgentMessage = {
  id: string
  content: string
  role: MessageRole
  timestamp: Date
  status: MessageStatus
  agentType?: string
}

export function useAgentChat() {
  const [messages, setMessages] = useState<AgentMessage[]>([
    {
      id: "welcome-1",
      content: "Hello! I'm your Agent System interface. I can help coordinate tasks between our specialized agents including designers, developers, and testers. What would you like to work on?",
      role: "agent",
      timestamp: new Date(),
      status: "completed",
      agentType: "micromanager"
    },
  ])
  const [isProcessing, setIsProcessing] = useState(false)

  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isProcessing) return

    const userMessage: AgentMessage = {
      id: `user-${Date.now()}`,
      content: content.trim(),
      role: "user",
      timestamp: new Date(),
      status: "sent"
    }

    setMessages(prev => [...prev, userMessage])
    setIsProcessing(true)

    // Simulate agent processing - in real implementation this would call Micromanager
    setTimeout(() => {
      const agentResponse: AgentMessage = {
        id: `agent-${Date.now()}`,
        content: `I understand you want to: "${userMessage.content}". Let me coordinate with the appropriate agents to help you with this task. I'll break this down into actionable steps and assign them to our specialized team.`,
        role: "agent",
        timestamp: new Date(),
        status: "completed",
        agentType: "micromanager"
      }

      setMessages(prev => [...prev, agentResponse])
      setIsProcessing(false)
    }, 1500)
  }, [isProcessing])

  const clearMessages = useCallback(() => {
    setMessages([
      {
        id: "welcome-1",
        content: "Hello! I'm your Agent System interface. I can help coordinate tasks between our specialized agents including designers, developers, and testers. What would you like to work on?",
        role: "agent",
        timestamp: new Date(),
        status: "completed",
        agentType: "micromanager"
      },
    ])
  }, [])

  return {
    messages,
    isProcessing,
    sendMessage,
    clearMessages
  }
}
