"use client"

import { useState, useCallback, useEffect } from "react"
import { CompleteAgentManager } from "@/components/agents/agent-manager-complete"
import { useSharedAgentState } from "@/components/agents/shared-agent-state"
import { AgentContext } from "@/components/agents/agent-base"
import { getChatHistoryService } from "@/services/chat-history"
import { createTaskStatusUpdate } from "@/utils/system-message-utils"
import type { AgentChatMessage } from "@/types/chat"

// Re-export for backward compatibility
export type AgentMessage = AgentChatMessage

export function useAgentChat() {
  const [messages, setMessages] = useState<AgentMessage[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const [agentManager] = useState(() => new CompleteAgentManager())
  const sharedState = useSharedAgentState()
  const [chatHistory] = useState(() => getChatHistoryService())

  // Load chat history on mount
  useEffect(() => {
    const loadHistory = async () => {
      try {
        const savedMessages = await chatHistory.loadChatHistory()

        if (savedMessages.length === 0) {
          // Add welcome message for new chats
          const welcomeMessage: AgentMessage = {
            id: "welcome-1",
            content: "Hello! I'm your Agent System interface. I can help coordinate tasks between our specialized agents including designers, developers, and testers. What would you like to work on?",
            role: "agent",
            timestamp: new Date(),
            status: "completed",
            agentType: "micromanager"
          }
          setMessages([welcomeMessage])
          await chatHistory.saveChatHistory([welcomeMessage])
        } else {
          setMessages(savedMessages)
        }
      } catch (error) {
        console.error('Failed to load chat history:', error)
        // Fallback to welcome message
        const welcomeMessage: AgentMessage = {
          id: "welcome-1",
          content: "Hello! I'm your Agent System interface. I can help coordinate tasks between our specialized agents including designers, developers, and testers. What would you like to work on?",
          role: "agent",
          timestamp: new Date(),
          status: "completed",
          agentType: "micromanager"
        }
        setMessages([welcomeMessage])
      } finally {
        setIsLoaded(true)
      }
    }

    loadHistory()
  }, [chatHistory])

  // Listen for agent messages for multi-agent responses
  useEffect(() => {
    const handleAgentMessage = (message: any) => {
      console.log('🔔 Agent message received:', message)

      // Check if this is a response from a delegated agent
      if (message.type === 'completion' || message.type === 'info') {
        const agentResponse: AgentMessage = {
          id: `agent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          content: message.message,
          role: "agent",
          timestamp: new Date(),
          status: "completed",
          agentType: message.agentId,
          agentId: message.agentId,
          taskId: message.taskId,
          metadata: message.metadata
        }

        // Add the agent response to messages
        setMessages(prev => [...prev, agentResponse])
      }
    }

    // Subscribe to agent manager messages
    agentManager.onMessage(handleAgentMessage)

    return () => {
      agentManager.offMessage(handleAgentMessage)
    }
  }, [agentManager])

  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isProcessing) return

    const userMessage: AgentMessage = {
      id: `user-${Date.now()}`,
      content: content.trim(),
      role: "user",
      timestamp: new Date(),
      status: "sent"
    }

    const updatedMessages = [...messages, userMessage]
    setMessages(updatedMessages)

    // Save immediately after user message
    await chatHistory.saveChatHistory(updatedMessages)

    setIsProcessing(true)

    try {
      // Create agent context for the task
      const context: AgentContext = {
        task: content.trim(),
        metadata: {
          source: 'agent_chat',
          requestedAt: Date.now(),
          chatMessageId: userMessage.id
        }
      }

      // Add task to shared state for tracking
      await sharedState.assignTask({
        agentId: 'micromanager',
        description: content.trim(),
        status: 'pending',
        priority: 'medium'
      })

      // Submit task to Micromanager agent
      const taskId = await agentManager.assignTask('micromanager', context, 'medium')

      // Add streaming message placeholder
      const streamingMessage: AgentMessage = {
        id: `streaming-${Date.now()}`,
        content: "",
        role: "agent",
        timestamp: new Date(),
        status: "processing",
        agentType: "micromanager",
        agentId: "micromanager",
        taskId,
        isStreaming: true,
        stream: true
      }

      setMessages(prev => [...prev, streamingMessage])
      setStreamingMessageId(streamingMessage.id)

      // Start streaming response simulation and wait for completion
      await simulateStreamingResponse(streamingMessage.id, taskId)

      // Emit system event for metrics
      emitAgentChatEvent('response_received', {
        agent: 'micromanager',
        taskId
      })

    } catch (error) {
      console.error('Agent chat error:', error)

      // Add error message
      const errorMessage: AgentMessage = {
        id: `error-${Date.now()}`,
        content: `❌ Agent Micromanager failed to respond: ${error instanceof Error ? error.message : 'Unknown error'}`,
        role: "system",
        timestamp: new Date(),
        status: "error",
        agentType: "micromanager"
      }

      setMessages(prev => {
        // Remove any processing/streaming messages
        const filtered = prev.filter(msg => msg.status !== "processing" && !msg.isStreaming)
        return [...filtered, errorMessage]
      })
    } finally {
      setIsProcessing(false)
      setStreamingMessageId(null)
    }
  }, [isProcessing, agentManager, sharedState])

  // Streaming response simulation function
  const simulateStreamingResponse = useCallback(async (messageId: string, taskId: string) => {
    try {
      // Wait for the actual agent response
      const response = await waitForAgentResponse(taskId)

      // Simulate streaming by breaking response into chunks
      const fullContent = response.content || "I understand your request. Let me coordinate with the appropriate agents to help you with this task."
      const words = fullContent.split(' ')
      let currentContent = ""

      // Stream words with realistic timing
      for (let i = 0; i < words.length; i++) {
        currentContent += (i > 0 ? ' ' : '') + words[i]

        setMessages(prev => prev.map(msg =>
          msg.id === messageId
            ? {
                ...msg,
                content: currentContent,
                status: "processing",
                isStreaming: true
              }
            : msg
        ))

        // Random delay between 50-200ms per word for realistic streaming
        await new Promise(resolve => setTimeout(resolve, Math.random() * 150 + 50))
      }

      // Finalize the message
      const finalMessages = messages.map(msg =>
        msg.id === messageId
          ? {
              ...msg,
              content: currentContent,
              status: "completed",
              isStreaming: false,
              tokensUsed: response.tokensUsed,
              metadata: {
                tokensUsed: response.tokensUsed,
                executionTime: response.executionTime,
                suggestions: response.suggestions
              }
            }
          : msg
      )

      setMessages(finalMessages)

      // Save completed response to history
      await chatHistory.saveChatHistory(finalMessages)

    } catch (error) {
      // Handle streaming error
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? {
              ...msg,
              content: `❌ Streaming failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
              status: "error",
              isStreaming: false
            }
          : msg
      ))
    } finally {
      setStreamingMessageId(null)
    }
  }, [])

  // Helper function to wait for agent response
  const waitForAgentResponse = useCallback(async (taskId: string, maxWaitTime = 30000) => {
    const startTime = Date.now()
    const pollInterval = 1000 // Poll every second

    return new Promise((resolve, reject) => {
      const poll = () => {
        // Check if task is completed in shared state
        const task = sharedState.tasks.find(t => t.id === taskId)

        if (task?.status === 'completed') {
          // Get the agent response from messages
          const agentMessage = sharedState.messages
            .filter(m => m.agentId === 'micromanager')
            .sort((a, b) => b.timestamp - a.timestamp)[0]

          resolve({
            content: agentMessage?.message || "Task completed successfully.",
            tokensUsed: task.metadata?.tokensUsed || 0,
            executionTime: Date.now() - startTime,
            suggestions: task.metadata?.suggestions || []
          })
          return
        }

        if (task?.status === 'failed') {
          reject(new Error(task.metadata?.error || 'Task execution failed'))
          return
        }

        // Check timeout
        if (Date.now() - startTime > maxWaitTime) {
          reject(new Error('Agent response timeout'))
          return
        }

        // Continue polling
        setTimeout(poll, pollInterval)
      }

      poll()
    })
  }, [sharedState])

  // Helper function to emit system events
  const emitAgentChatEvent = useCallback((eventType: string, data: any) => {
    try {
      // Add event to shared state messages for tracking
      sharedState.addMessage({
        agentId: 'system',
        message: `Agent Chat Event: ${eventType}`,
        timestamp: Date.now(),
        type: 'info',
        metadata: data
      })

      // Log for debugging
      console.log(`🔔 Agent Chat Event: ${eventType}`, data)
    } catch (error) {
      console.warn('Failed to emit agent chat event:', error)
    }
  }, [sharedState])

  const clearMessages = useCallback(async () => {
    const welcomeMessage: AgentMessage = {
      id: "welcome-1",
      content: "Hello! I'm your Agent System interface. I can help coordinate tasks between our specialized agents including designers, developers, and testers. What would you like to work on?",
      role: "agent",
      timestamp: new Date(),
      status: "completed",
      agentType: "micromanager"
    }

    setMessages([welcomeMessage])

    // Clear history and save welcome message
    await chatHistory.clearChatHistory()
    await chatHistory.saveChatHistory([welcomeMessage])
  }, [chatHistory])

  return {
    messages,
    isProcessing,
    streamingMessageId,
    isLoaded,
    sendMessage,
    clearMessages,
    chatHistory
  }
}
