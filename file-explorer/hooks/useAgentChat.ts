"use client"

import { useState, useCallback, useEffect } from "react"
import { CompleteAgentManager } from "@/components/agents/agent-manager-complete"
import { useSharedAgentState } from "@/components/agents/shared-agent-state"
import { AgentContext } from "@/components/agents/agent-base"

type MessageRole = "user" | "agent" | "system"
type MessageStatus = "sending" | "sent" | "processing" | "completed" | "error"

export type AgentMessage = {
  id: string
  content: string
  role: MessageRole
  timestamp: Date
  status: MessageStatus
  agentType?: string
  taskId?: string
  metadata?: Record<string, any>
}

export function useAgentChat() {
  const [messages, setMessages] = useState<AgentMessage[]>([
    {
      id: "welcome-1",
      content: "Hello! I'm your Agent System interface. I can help coordinate tasks between our specialized agents including designers, developers, and testers. What would you like to work on?",
      role: "agent",
      timestamp: new Date(),
      status: "completed",
      agentType: "micromanager"
    },
  ])
  const [isProcessing, setIsProcessing] = useState(false)
  const [agentManager] = useState(() => new CompleteAgentManager())
  const sharedState = useSharedAgentState()

  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isProcessing) return

    const userMessage: AgentMessage = {
      id: `user-${Date.now()}`,
      content: content.trim(),
      role: "user",
      timestamp: new Date(),
      status: "sent"
    }

    setMessages(prev => [...prev, userMessage])
    setIsProcessing(true)

    try {
      // Create agent context for the task
      const context: AgentContext = {
        task: content.trim(),
        metadata: {
          source: 'agent_chat',
          requestedAt: Date.now(),
          chatMessageId: userMessage.id
        }
      }

      // Add task to shared state for tracking
      await sharedState.assignTask({
        agentId: 'micromanager',
        description: content.trim(),
        status: 'pending',
        priority: 'medium'
      })

      // Submit task to Micromanager agent
      const taskId = await agentManager.assignTask('micromanager', context, 'medium')

      // Add processing message
      const processingMessage: AgentMessage = {
        id: `processing-${Date.now()}`,
        content: "Processing your request...",
        role: "agent",
        timestamp: new Date(),
        status: "processing",
        agentType: "micromanager",
        taskId
      }

      setMessages(prev => [...prev, processingMessage])

      // Wait for agent response (poll for completion)
      const response = await waitForAgentResponse(taskId)

      // Remove processing message and add real response
      setMessages(prev => {
        const filtered = prev.filter(msg => msg.id !== processingMessage.id)
        const agentResponse: AgentMessage = {
          id: `agent-${Date.now()}`,
          content: response.content || "Task completed successfully.",
          role: "agent",
          timestamp: new Date(),
          status: "completed",
          agentType: "micromanager",
          taskId,
          metadata: {
            tokensUsed: response.tokensUsed,
            executionTime: response.executionTime,
            suggestions: response.suggestions
          }
        }
        return [...filtered, agentResponse]
      })

      // Emit system event for metrics
      emitAgentChatEvent('response_received', {
        agent: 'micromanager',
        taskId,
        tokensUsed: response.tokensUsed,
        executionTime: response.executionTime
      })

    } catch (error) {
      console.error('Agent chat error:', error)

      // Add error message
      const errorMessage: AgentMessage = {
        id: `error-${Date.now()}`,
        content: `❌ Agent Micromanager failed to respond: ${error instanceof Error ? error.message : 'Unknown error'}`,
        role: "system",
        timestamp: new Date(),
        status: "error",
        agentType: "micromanager"
      }

      setMessages(prev => {
        // Remove any processing messages
        const filtered = prev.filter(msg => msg.status !== "processing")
        return [...filtered, errorMessage]
      })
    } finally {
      setIsProcessing(false)
    }
  }, [isProcessing, agentManager, sharedState])

  // Helper function to wait for agent response
  const waitForAgentResponse = useCallback(async (taskId: string, maxWaitTime = 30000) => {
    const startTime = Date.now()
    const pollInterval = 1000 // Poll every second

    return new Promise((resolve, reject) => {
      const poll = () => {
        // Check if task is completed in shared state
        const task = sharedState.tasks.find(t => t.id === taskId)

        if (task?.status === 'completed') {
          // Get the agent response from messages
          const agentMessage = sharedState.messages
            .filter(m => m.agentId === 'micromanager')
            .sort((a, b) => b.timestamp - a.timestamp)[0]

          resolve({
            content: agentMessage?.message || "Task completed successfully.",
            tokensUsed: task.metadata?.tokensUsed || 0,
            executionTime: Date.now() - startTime,
            suggestions: task.metadata?.suggestions || []
          })
          return
        }

        if (task?.status === 'failed') {
          reject(new Error(task.metadata?.error || 'Task execution failed'))
          return
        }

        // Check timeout
        if (Date.now() - startTime > maxWaitTime) {
          reject(new Error('Agent response timeout'))
          return
        }

        // Continue polling
        setTimeout(poll, pollInterval)
      }

      poll()
    })
  }, [sharedState])

  // Helper function to emit system events
  const emitAgentChatEvent = useCallback((eventType: string, data: any) => {
    try {
      // Add event to shared state messages for tracking
      sharedState.addMessage({
        agentId: 'system',
        message: `Agent Chat Event: ${eventType}`,
        timestamp: Date.now(),
        type: 'info',
        metadata: data
      })

      // Log for debugging
      console.log(`🔔 Agent Chat Event: ${eventType}`, data)
    } catch (error) {
      console.warn('Failed to emit agent chat event:', error)
    }
  }, [sharedState])

  const clearMessages = useCallback(() => {
    setMessages([
      {
        id: "welcome-1",
        content: "Hello! I'm your Agent System interface. I can help coordinate tasks between our specialized agents including designers, developers, and testers. What would you like to work on?",
        role: "agent",
        timestamp: new Date(),
        status: "completed",
        agentType: "micromanager"
      },
    ])
  }, [])

  return {
    messages,
    isProcessing,
    sendMessage,
    clearMessages
  }
}
